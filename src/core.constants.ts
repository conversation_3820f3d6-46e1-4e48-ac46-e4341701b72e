export enum CollectionStatus {
  PRELAUNCH = "PRELAUNCH",
  ACTIVE = "ACTIVE",
  DELETED = "DELETED",
}

export interface Collection {
  id: string;
  collectionId: string;
  name: string;
  logoUrl: string;
  description: string;
  status: CollectionStatus;
  createdAt: Date;
  updatedAt: Date;
}

export const COLLECTION_STATUS_TEXT = {
  [CollectionStatus.PRELAUNCH]: "Pre-launch",
  [CollectionStatus.ACTIVE]: "Active",
  [CollectionStatus.DELETED]: "Deleted",
};

export enum Role {
  ADMIN = "admin",
  USER = "user",
}
