"use client";

import { AppHeader } from "@/app/home-components/app-header";
import { App } from "@/core.constants";
import { Drawer } from "vaul";
import { EmergencyActionsSection } from "./shared/emergency-section";
import { PhoneNumberSection } from "./shared/phone-number-section";
import { RecommendationsSection } from "./shared/recommendations-section";
import { SecurityFeaturesSection } from "./shared/security-features-section";
import { SecurityInstructionsSection } from "./shared/security-instructions-section";

interface AppDetailsDrawerProps {
  app: App | null;
  isOpen: boolean;
  onClose: () => void;
}

export const AppDetailsDrawer = ({
  app,
  isOpen,
  onClose,
}: AppDetailsDrawerProps) => {
  if (!app) return null;

  return (
    <Drawer.Root open={isOpen} onOpenChange={onClose}>
      <Drawer.Portal>
        <Drawer.Overlay className="fixed inset-0 bg-black/40 z-50" />
        <Drawer.Content className="bg-white dark:bg-neutral-900 flex flex-col rounded-t-[10px] h-[85vh] mt-24 fixed bottom-0 left-0 right-0 z-50">
          <Drawer.Title></Drawer.Title>
          <div className="p-4 bg-white dark:bg-neutral-900 rounded-t-[10px] flex-1 overflow-y-auto pb-[100px]">
            <div className="mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-neutral-300 dark:bg-neutral-600 mb-6" />

            <AppHeader app={app} />

            <div className="space-y-6">
              <SecurityFeaturesSection app={app} variant="drawer" />
              <PhoneNumberSection app={app} variant="drawer" />
              <EmergencyActionsSection app={app} variant="drawer" />
              <SecurityInstructionsSection app={app} variant="drawer" />
              <RecommendationsSection app={app} variant="drawer" />
            </div>
          </div>
        </Drawer.Content>
      </Drawer.Portal>
    </Drawer.Root>
  );
};
