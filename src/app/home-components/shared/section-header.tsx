"use client";

import React from "react";

interface SectionHeaderProps {
  icon: React.ElementType;
  title: string;
  iconColorClass: string;
  variant?: "modal" | "drawer";
}

export const SectionHeader = ({
  icon: Icon,
  title,
  iconColorClass,
  variant = "drawer",
}: SectionHeaderProps) => {
  if (variant === "modal") {
    return (
      <div className="flex items-center space-x-3 mb-4">
        <div
          className={`w-8 h-8 ${iconColorClass} rounded-lg flex items-center justify-center`}
        >
          <Icon className="w-3 h-3 text-white" />
        </div>
        <h2 className="text-2xl font-bold text-white">{title}</h2>
        <div className="flex-1 h-px bg-gradient-to-r from-white/20 to-transparent" />
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-2 mb-4">
      <Icon className={`w-5 h-5 ${iconColorClass}`} />
      <h2 className="text-lg font-semibold text-neutral-900 dark:text-neutral-100">
        {title}
      </h2>
    </div>
  );
};
