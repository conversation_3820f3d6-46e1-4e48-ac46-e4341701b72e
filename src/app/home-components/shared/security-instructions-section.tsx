"use client";

import React from "react";
import Image from "next/image";
import { Markdown } from "@/components/ui/markdown";
import { App } from "@/core.constants";
import { CollapsibleSection } from "./collapsible-section";
import { Activity } from "lucide-react";

interface SecurityInstructionsSectionProps {
  app: App;
  variant?: "modal" | "drawer";
}

export const SecurityInstructionsSection = ({
  app,
  variant = "drawer",
}: SecurityInstructionsSectionProps) => {
  if (app.emergency.instructions.length === 0) return null;

  const isModal = variant === "modal";

  return (
    <CollapsibleSection
      icon={Activity}
      title="Security Instructions"
      iconColorClass={
        isModal
          ? "bg-gradient-to-r from-purple-500 to-pink-500"
          : "text-purple-500"
      }
      variant={variant}
    >
      <div className="space-y-6">
        {app.emergency.instructions.map((instruction, index) => (
          <div
            key={`instruction-${index}-${instruction.description.slice(0, 20)}`}
            className={
              isModal
                ? "rounded-2xl border border-white/10 bg-gradient-to-br from-neutral-900/50 to-neutral-800/50 backdrop-blur-sm p-6 hover:border-white/20 transition-all duration-300"
                : "rounded-lg border border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800 p-4"
            }
          >
            <div className="mb-4">
              <Markdown
                content={instruction.description}
                className={
                  isModal
                    ? "text-neutral-300"
                    : "text-neutral-700 dark:text-neutral-300"
                }
              />
            </div>
            {instruction.imageLink && (
              <div className="mt-4">
                <Image
                  src={instruction.imageLink}
                  alt="Security instruction visual guide"
                  width={400}
                  height={200}
                  className={`w-full object-cover ${
                    isModal
                      ? "rounded-xl border border-white/10 shadow-lg"
                      : "rounded-lg border border-neutral-200 dark:border-neutral-700"
                  }`}
                />
              </div>
            )}
          </div>
        ))}
      </div>
    </CollapsibleSection>
  );
};
